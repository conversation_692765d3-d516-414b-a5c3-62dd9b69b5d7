package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/GoAdminGroup/go-admin/engine"
	"github.com/GoAdminGroup/go-admin/modules/config"
	"github.com/GoAdminGroup/go-admin/modules/language"
	"github.com/GoAdminGroup/go-admin/template"
	"github.com/GoAdminGroup/go-admin/template/chartjs"
	"github.com/gin-gonic/gin"
	_ "github.com/GoAdminGroup/go-admin/adapter/gin"
	_ "github.com/GoAdminGroup/go-admin/modules/db/drivers/postgres"

	"gobackend-hvac-kratos/internal/admin/models"
)

// 🚀 HVAC CRM Admin Dashboard - Potężny system zarządzania całym ekosystemem
// Wykorzystuje go-admin framework dla profesjonalnego interfejsu administracyjnego

const (
	ServiceName    = "hvac-admin-dashboard"
	ServiceVersion = "1.0.0"
	DefaultPort    = "8090"
)

func main() {
	log.Println("🚀 Starting HVAC CRM Admin Dashboard...")
	log.Println("🎯 Framework: go-admin")
	log.Println("🔧 Target: Complete HVAC ecosystem management")

	// Initialize Gin router
	r := gin.Default()

	// Initialize go-admin engine
	eng := engine.Default()

	// Load configuration
	cfg := loadAdminConfig()

	// Add chartjs component for visualizations
	template.AddComp(chartjs.NewChart())

	// Configure go-admin
	if err := eng.AddConfig(&cfg).
		AddGenerators(models.GetGenerators()).
		Use(r); err != nil {
		log.Fatal("Failed to initialize admin engine:", err)
	}

	// Add custom routes for HVAC-specific functionality
	setupCustomRoutes(r, eng)

	// Setup graceful shutdown
	srv := &http.Server{
		Addr:    ":" + getPort(),
		Handler: r,
	}

	// Start server in goroutine
	go func() {
		log.Printf("🌐 HVAC Admin Dashboard starting on port %s", getPort())
		log.Printf("📊 Dashboard URL: http://localhost:%s/admin", getPort())
		log.Printf("🔗 API URL: http://localhost:%s/api", getPort())
		
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Server failed to start:", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("🛑 Shutting down HVAC Admin Dashboard...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("✅ HVAC Admin Dashboard stopped gracefully")
}

func loadAdminConfig() config.Config {
	return config.Config{
		// Database configuration - using existing PostgreSQL
		Databases: config.DatabaseList{
			"default": {
				Host:       "**************",
				Port:       "5432",
				User:       "hvacdb",
				Pwd:        "blaeritipol",
				Name:       "hvacdb",
				MaxIdleCon: 50,
				MaxOpenCon: 150,
				Driver:     "postgres",
			},
		},
		
		// Admin panel configuration
		UrlPrefix: "admin",
		
		// File storage configuration
		Store: config.Store{
			Path:   "./uploads",
			Prefix: "uploads",
		},
		
		// Language configuration
		Language: language.EN,
		
		// Development mode
		Debug: true,
		
		// Logging configuration
		InfoLogPath:   "./logs/admin-info.log",
		AccessLogPath: "./logs/admin-access.log",
		ErrorLogPath:  "./logs/admin-error.log",
		
		// Theme configuration
		ColorScheme: "skin-blue",
		
		// Session configuration
		SessionLifeTime: 7200, // 2 hours
		
		// Security configuration
		LoginUrl:    "/admin/login",
		NoLimitLoginIP: false,
		
		// Custom configuration for HVAC CRM
		Custom: map[string]interface{}{
			"app_name":        "HVAC CRM Admin",
			"app_description": "Comprehensive HVAC business management system",
			"company_name":    "Fulmark Klimatyzacja",
			"support_email":   "<EMAIL>",
		},
	}
}

func setupCustomRoutes(r *gin.Engine, eng *engine.Engine) {
	// API routes for HVAC-specific functionality
	api := r.Group("/api")
	{
		// System monitoring
		api.GET("/system/status", handleSystemStatus)
		api.GET("/system/metrics", handleSystemMetrics)
		
		// HVAC business metrics
		api.GET("/hvac/dashboard", handleHVACDashboard)
		api.GET("/hvac/customers/stats", handleCustomerStats)
		api.GET("/hvac/jobs/stats", handleJobStats)
		api.GET("/hvac/revenue/stats", handleRevenueStats)
		
		// AI & Transcription monitoring
		api.GET("/ai/status", handleAIStatus)
		api.GET("/transcription/stats", handleTranscriptionStats)
		
		// Email intelligence
		api.GET("/email/stats", handleEmailStats)
		api.GET("/email/sentiment", handleEmailSentiment)
	}
	
	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": ServiceName,
			"version": ServiceVersion,
			"time":    time.Now().Format(time.RFC3339),
		})
	})
}

func getPort() string {
	if port := os.Getenv("ADMIN_PORT"); port != "" {
		return port
	}
	return DefaultPort
}

// Custom handlers for HVAC-specific functionality
func handleSystemStatus(c *gin.Context) {
	// TODO: Implement system status monitoring
	c.JSON(http.StatusOK, gin.H{
		"status": "operational",
		"services": map[string]interface{}{
			"gobackend_kratos": "running",
			"postgresql":       "connected",
			"redis":           "connected",
			"mongodb":         "connected",
			"minio":           "connected",
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleSystemMetrics(c *gin.Context) {
	// TODO: Implement system metrics collection
	c.JSON(http.StatusOK, gin.H{
		"cpu_usage":    "45%",
		"memory_usage": "62%",
		"disk_usage":   "38%",
		"uptime":       "5d 12h 30m",
		"timestamp":    time.Now().Format(time.RFC3339),
	})
}

func handleHVACDashboard(c *gin.Context) {
	// TODO: Implement HVAC dashboard data
	c.JSON(http.StatusOK, gin.H{
		"active_jobs":      25,
		"pending_jobs":     8,
		"completed_today":  12,
		"revenue_today":    "8,450 PLN",
		"customer_satisfaction": 4.7,
		"technicians_active": 6,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleCustomerStats(c *gin.Context) {
	// TODO: Implement customer statistics
	c.JSON(http.StatusOK, gin.H{
		"total_customers": 1247,
		"new_this_month": 23,
		"vip_customers":  45,
		"active_customers": 892,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleJobStats(c *gin.Context) {
	// TODO: Implement job statistics
	c.JSON(http.StatusOK, gin.H{
		"total_jobs": 3456,
		"completed": 3201,
		"in_progress": 25,
		"scheduled": 230,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleRevenueStats(c *gin.Context) {
	// TODO: Implement revenue statistics
	c.JSON(http.StatusOK, gin.H{
		"monthly_revenue": "125,000 PLN",
		"yearly_revenue": "1,200,000 PLN",
		"avg_job_value": "850 PLN",
		"growth_rate": "+12%",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleAIStatus(c *gin.Context) {
	// TODO: Implement AI services monitoring
	c.JSON(http.StatusOK, gin.H{
		"bielik_v3": "running",
		"gemma3": "running",
		"lm_studio": "connected",
		"models_loaded": 3,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleTranscriptionStats(c *gin.Context) {
	// TODO: Implement transcription statistics
	c.JSON(http.StatusOK, gin.H{
		"processed_today": 45,
		"queue_length": 3,
		"avg_processing_time": "28s",
		"accuracy_rate": "94%",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleEmailStats(c *gin.Context) {
	// TODO: Implement email statistics
	c.JSON(http.StatusOK, gin.H{
		"processed_today": 127,
		"pending_analysis": 8,
		"auto_responses": 23,
		"sentiment_positive": "78%",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

func handleEmailSentiment(c *gin.Context) {
	// TODO: Implement email sentiment analysis
	c.JSON(http.StatusOK, gin.H{
		"positive": 78,
		"neutral": 15,
		"negative": 7,
		"trend": "improving",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
